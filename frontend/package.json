{"name": "goalmatic", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "lint": "eslint ./src --ext .ts,.vue --fix", "generate-icon-name-enum": "node bin/generate-icon-name-enum.js", "generate-sprite": "svgstore -o ./src/assets/icons/sprite.svg ./src/assets/icons/source/*.svg", "prestart": "yarn generate-sprite && yarn generate-icon-name-enum", "preview": "nuxt preview", "postinstall": "nuxt prepare", "deploy": "firebase deploy --only functions", "ds": "firebase deploy --only functions:", "emu": "firebase emulators:start --import=./dummy_data --export-on-exit"}, "dependencies": {"@ai-sdk/google": "^1.2.12", "@ai-sdk/openai": "^1.1.9", "@firebasegen/default-connector": "link:dataconnect-generated/js/default-connector", "@genkit-ai/googleai": "^0.9.12", "@google/generative-ai": "^0.21.0", "@headlessui/vue": "^1.7.23", "@iconify/vue": "^4.3.0", "@nuxt/kit": "^3.17.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tiptap/extension-color": "^2.7.4", "@tiptap/extension-mention": "^2.11.7", "@tiptap/extension-text-style": "^2.7.4", "@tiptap/pm": "^2.7.4", "@tiptap/starter-kit": "^2.7.4", "@tiptap/vue-3": "^2.7.4", "@vueuse/core": "^10.11.1", "@vueuse/nuxt": "^9.3.1", "ai": "^4.3.9", "cron-parser": "^5.2.0", "cronstrue": "^2.61.0", "firebase": "^9.12.1", "firebase-admin": "^11.2.1", "firebase-functions": "^4.1.0", "genkit": "^0.9.12", "googleapis": "^144.0.0", "gsap": "^3.13.0", "lucide-vue-next": "^0.448.0", "marked": "^15.0.6", "moment": "^2.30.1", "motion-v": "^0.10.0", "nuxt": "^3.17.1", "nuxt-gtag": "1.1.2", "nuxt-security": "^2.0.0-rc.6", "posthog-js": "^1.215.6", "preact": "^10.25.4", "radix-vue": "^1.9.13", "sass": "^1.81.0", "sortablejs": "^1", "split.js": "^1.6.5", "uuid": "^9.0.0", "vue": "^3.4.27", "vue-cal": "^5.0.1-rc.1", "vue-datepicker-next": "^1.0.3", "vue-draggable-next": "^2.2.1", "vue-gtag-next": "^1.14.0", "vue-router": "^4.3.2", "@tiptap/suggestion": "^2.0.0-beta.75", "tippy.js": "^6.3.7"}, "devDependencies": {"@inspira-ui/plugins": "^0.0.1", "@nuxt/devtools": "^0.6.7", "@nuxt/postcss8": "^1.1.3", "@nuxtjs/eslint-config-typescript": "^10.0.0", "@nuxtjs/tailwindcss": "6.1.3", "@types/google.maps": "^3.53.4", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.31.0", "@vue/eslint-config-prettier": "^7.0.0", "autoprefixer": "^10.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "8.21.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-nuxt": "3.2.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-unused-imports": "4.0.0", "eslint-plugin-vue": "9.3.0", "prettier-plugin-tailwindcss": "0.1.10", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^4.6.4", "vite-plugin-eslint": "^1.8.1"}}