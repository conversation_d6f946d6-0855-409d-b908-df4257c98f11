@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--border: 1.5px;
}

@layer base {
	.controls {
		@apply flex items-center justify-between border-b border-[#E9EAEB] pb-5 mb-4;
		.options {
			@apply items-center gap-2;
		}
	}

	.tabs {
		@apply w-full md:w-auto flex items-start gap-2 rounded-lg bg-[#f9f8fb] p-1;

		.tab-btn {
			@apply w-full md:w-auto flex items-center justify-center font-medium p-2 gap-2.5 rounded-none text-xs text-[#19213d];

			&.active {
				border-radius: 4px;
				border: 1px solid #f0f2f5;
				background: #fcfcfd;
				box-shadow: 0px 1px 3px 0px rgba(25, 33, 61, 0.1);
				display: flex;
				padding: 8px;
				justify-content: center;
				align-items: center;
				gap: 10px;
			}
		}
	}

	.transite {
		@apply transition-all duration-75 ease-linear;
	}
	.center {
		@apply flex items-center justify-center;
	}

	.field {
		@apply flex flex-col gap-2 w-full items-start;
	}

	label,
	.label {
		text-underline-position: from-font;
		text-decoration-skip-ink: none;
		@apply font-medium text-sm text-headline text-left;
	}

	.input {
		@apply w-full text-dark text-sm px-3 py-2 rounded-lg outline-none  border border-[#EBEEF2] focus:border-[#7F4AF1] 
        bg-light transition-all duration-200 ease-in disabled:bg-[#f1f2f5b4] disabled:text-gray-500;
	}

	.input-field {
		@apply input;
	}

	.input-select {
		@apply input;
	}

	.input-textarea {
		@apply input py-4;
	}

	.main-layout {
		@apply container flex justify-center items-center min-h-screen py-12 px-2.5;
	}

	.auth-box {
		@apply w-[450px] mx-auto relative max-w-[100%] bg-[#f8f9fc6c] px-9 py-7 flex flex-col items-center gap-4  rounded-md border border-dark;
		-webkit-perspective: 1000px;
		perspective: 1000px;
		-webkit-transform-style: preserve-3d;
		transform-style: preserve-3d;
	}

	.auth-title {
		@apply font-light text-2xl text-center flex w-full items-center justify-center relative text-dark;
	}

	.btn-text {
		@apply border rounded gap-2 bg-light !py-1 !px-3 text-xs font-semibold;
	}
	.default-pill {
		@apply text-xs bg-[#F2F2F2] text-dark px-2 py-0.5 rounded hidden md:inline-flex;
	}
	.btn-icon {
		@apply btn !shadow-none  border !border-[#CFBBFA];
	}

	.button_shadow {
		box-shadow: 0px 0px 3px 0px #5f1ded;
	}

	.custom_shadow:hover {
		box-shadow: -1px -1px 1px 0.3px rgba(255, 255, 255, 0.25);
	}
	.custom-btn {
		@apply center px-4 py-2.5 rounded-lg bg-primary text-white text-sm border border-white font-semibold button_shadow;
	}

	.btn-sm {
		@apply rounded-lg px-4 py-1.5 flex justify-center items-center font-medium disabled:bg-gray-500 disabled:text-dark disabled:cursor-not-allowed  box-border  border border-secondary hover:shadow-[4px_3.5px_0px_0px_#000] transite;
	}
	.btn {
		@apply center px-6 py-[11px]  rounded-[6px] text-sm   font-medium  disabled:bg-gray-500 disabled:text-dark disabled:cursor-not-allowed transite;
	}
	.btn2 {
		@apply center px-6 py-[11px] rounded-[6px] text-sm font-semibold  border-[3px] border-[#E9E9E9] disabled:text-dark disabled:cursor-not-allowed transite;
	}

	.btn-primary {
		@apply btn bg-primary text-light hover:scale-95;
	}

	.btn-primary-inverse {
		@apply btn bg-light text-primary;
	}
	.btn-secondary {
		@apply btn bg-dark text-light;
	}
	.btn-outline {
		@apply btn bg-light_primary text-dark !border border-[#E1E6EC] !shadow-sm hover:scale-95;
	}
	.btn_shadow {
		@apply transite;
		box-shadow: 4.286px 3.482px 0px 0px #000;
	}

	.auth-form {
		@apply flex flex-col gap-4 overflow-y-auto w-full items-center;
	}

	.btn_flat {
		border-width: var(--border);
		@apply rounded-md px-4 py-[11px] flex justify-center items-center font-medium disabled:bg-gray-400 disabled:text-dark disabled:cursor-not-allowed box-border   hover:shadow-sm transite;
	}

	.menu-btn {
		@apply flex items-center justify-center font-semibold border-2 border-dark text-dark hover:scale-105 duration-200 w-[190px] h-11 px-6 text-base rounded-md;

		&:hover {
			@apply bg-dark text-light;
		}
	}

	.form-height {
		@apply md:h-auto overflow-y-auto;
	}

	.bg-modal {
		position: fixed;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, 0.5);
		width: 100vw;
		max-width: 100vw;
		min-height: 100%;
		z-index: 100;
		display: flex;
		justify-content: center;
		align-items: center;
		backdrop-filter: blur(1.5px);
	}

	.modal-title {
		@apply font-medium md:text-lg text-start w-full text-dark p-5 pb-0;
	}

	.modal {
		box-shadow: 0px 1px 2px 0px #0000000d;
		@apply bg-light flex flex-col items-start gap-4  max-h-screen rounded-2xl sm:w-[470px] max-w-[100%] overflow-y-auto  transite;
	}

	.modal-btn {
		@apply center px-4 py-2.5  text-light hover:text-light bg-primary transite w-full justify-center disabled:bg-gray-500 cursor-pointer disabled:cursor-not-allowed rounded-lg;
	}

	.modal-btn-secondary {
		@apply rounded-md center px-4 py-3 border-2 border-secondary text-secondary hover:text-light hover:bg-secondary transite w-full justify-center disabled:bg-gray-500 cursor-pointer disabled:cursor-not-allowed;
	}

	.modal-btn-sm {
		@apply modal-btn h-10 center px-7;
	}

	.modal-btn-xs {
		@apply modal-btn center px-4 h-8 text-xs;
	}

	.modal-btn-red {
		@apply rounded-md center px-4 py-3 border-2 border-danger text-danger hover:text-light hover:bg-danger transite w-full flex justify-center disabled:bg-gray-500 cursor-pointer disabled:cursor-not-allowed;
	}
	.card_ans {
		@apply p-2 border border-line w-full rounded-md;
	}
	.card_ans_sm {
		@apply px-4 py-3 border border-grey_one w-auto rounded-lg;
	}
	.pill {
		@apply flex items-center gap-1 text-xs  py-1 px-1.5 rounded bg-hover border;
	}

	.pc {
		@apply !hidden lg:!flex;
	}

	.tab {
		@apply !hidden md:!flex;
	}

	.mobile {
		@apply !flex lg:!hidden;
	}

	.card_btn {
		@apply flex gap-2 items-center text-sm font-medium  border border-line   hover:btn_shadow px-2 py-1 rounded-md;
	}

	.page-title {
		@apply text-dark font-bold text-2xl;
	}

	.flow-empty-btn {
		@apply w-full bg-hover border border-dashed  rounded-lg flex justify-center items-center p-4 hover:border-primary hover:scale-[101%] gap-2 transite;
	}
	input:disabled,
	textarea:disabled,
	select:disabled {
		@apply bg-[#e3e3e352];
	}
	.searchInput {
		@apply w-full text-sm transition-all duration-200 ease-in border border-[#F0F2F5] shadow-sm rounded-lg outline-none;
		display: flex;
		height: 42px;
		min-height: 39px;
		padding: 6px 12px 6px 36px;
		align-items: center;
		align-self: stretch;
	}
	.searchInput:focus {
		outline: none;
		border-color: #9a6bff;
		background-color: white;
		box-shadow: 0 0 0 3px rgba(154, 107, 255, 0.1);
	}

	.searchInput:focus + .searchIcon {
		color: #9a6bff;
	}

	.searchInput::placeholder {
		color: #9ca3af;
	}
	.landing-btn {
		@apply bg-primary text-light btn-primary;
		border-radius: 8px !important;
		box-shadow: 0px 2px 5px 0px rgba(98, 20, 201, 0.22);
		position: relative;
	}

	.landing-btn::before {
		content: '';
		position: absolute;
		top: -4px;
		left: -4px;
		right: -4px;
		bottom: -4px;
		border: 2px solid var(--primary);
		border-radius: 12px;
		z-index: -1;
	}

	.landing-btn-secondary {
		@apply bg-light text-dark btn-primary;
		border-radius: 8px !important;
		box-shadow: 0px 2px 5px 0px rgba(98, 20, 201, 0.22);
		position: relative;
	}

	.landing-btn-secondary::before {
		content: '';
		position: absolute;
		top: -4px;
		left: -4px;
		right: -4px;
		bottom: -4px;
		border: 2px solid var(--line);
		border-radius: 12px;
		z-index: -1;
	}
}
