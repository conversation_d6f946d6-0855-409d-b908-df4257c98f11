:root {
	--primary: #601ded;
	--secondary: #000000;
	--tertiary: #f5f1fe;
	--secondaryLight: #ccc2ef;
	--light_primary: #f7f9fb;
	--faded_primary: #03030321;
	--sea: #554a27;
	--danger: #ff2a2a;
	--light: #ffffff;
	--dark: #000000;
	--light_grey: #a4a4a4;
	--grey: #f7f8fa;
	--grey_one: #e9e9e9;
	--grey_two: #2b2b38;
	--grey_three: #f2f2f2;
	--grey_four: #646368;
	--grey_six: #908f93;
	--grey_eight: #bcbcbe;
	--grey_nine: #d3d2d4;
	--soft_purple: #b5afff;
	--baby_purple: #766cff;
	--dark_purple: #0e0ba1;
	--line: #dce3ea;
	--hover: #eaebec99;
	--greenx: #0a4c08;
	--headline: #212028;
	--subText: #344054;
	--text-secondary: #646368;
	--mw: 760px;
	--mw2: 890px;
}

/* [theme='light'] {
    --primary: #000000;
    --secondary: #4f1ded;
    --secondaryLight: #ccc2ef;
    --light_primary: #c4c4d0;
    --faded_primary: #03030321;
    --sea: #554a27;
    --danger: #f02d3ace;
    --light: #ffffff;
    --dark: #000000;
    --grey: #f4f7fa;
    --grey_two: #2b2b38bb;
    --grey_four: #aaaaaf;
    --line: #737882c9;
    --hover: #e0dcedb2;
    --greenx: #28a737;
} */

/* Dark mode for root variable  */
/* [theme='dark'] {
    --primary: #ffffff;
    --secondary: #b38ce5;
    --secondaryLight: #a897e6;
    --light_primary: #1e1e2d;
    --faded_primary: #ffffff21;
    --sea: #26507f;
    --danger: #ff464fba;
    --light: #1e1e2d;
    --dark: #d8d8e6;
    --grey: #18181f;
    --grey_two: #9999a6;
    --grey_four: #4c4c56;
    --line: #737882c9;
    --hover: #3b2d6b7b;
    --greenx: #28a737;
} */
