<template>
	<ClientOnly>
		<NuxtLayout name="custom-header-dashboard">
			<template #header>
				<FlowHeader v-if="isOwner(flowDetails)" />
				<DashboadHeader v-else />
			</template>

			<main :class="['p-4', 'sm:p-6', 'h-screen', { 'flow-bg': isOwner(flowDetails) }]">
				<section class="flex flex-col gap-4 center pt-10 px-4 md:px-10 2xl:max-w-5xl max-w-7xl mx-auto w-full">
					<FlowsIdHeader v-if="!isOwner(flowDetails) && flowDetails.id" :flow-data="flowDetails" :loading="loading" />
				</section>

				<FlowsIdToolbar v-if="isOwner(flowDetails)" :current-tab="currentTab" :flow-data="flowDetails" @update:current-tab="currentTab = $event" />
				<section v-if="loading" class="flex flex-col gap-4 pt-10 px-4 md:px-10 w-full max-w-5xl mx-auto">
					<FlowsIdLoader />
				</section>

				<!-- Flow details -->
				<div v-else-if="flowDetails && Object.keys(flowDetails).length > 0">
					<!-- Use the Details component -->
					<FlowsIdDetails
						:current-tab="currentTab"
						:flow-data="flowDetails"
						:flow-logs="flowLogs"
						:flow-logs-loading="flowLogsLoading"
						@refresh-logs="refreshFlowLogs"
					/>
				</div>

				<FlowsIdErrorState v-else />
			</main>
		</NuxtLayout>
	</ClientOnly>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import FlowHeader from '@/components/layouts/header/FlowHeader.vue'
import DashboadHeader from '@/components/layouts/DashboadHeader.vue'
import FlowsIdHeader from '@/components/flows/id/Header.vue'
import FlowsIdDetails from '@/components/flows/id/Details.vue'
import FlowsIdLoader from '@/components/flows/id/Loader.vue'
import FlowsIdErrorState from '@/components/flows/id/ErrorState.vue'
import { useFetchFlowById } from '@/composables/dashboard/flows/id'
import { useEditFlow } from '@/composables/dashboard/flows/edit'
import { useCustomHead } from '@/composables/core/head'
import { useFlowOwner } from '@/composables/dashboard/flows/owner'
import { useHeaderTitle } from '@/composables/core/headerTitle'
const route = useRoute()
const flowId = route.params.id as string
const { fetchFlowById, loading, flowDetails } = useFetchFlowById()
const { fetchFlowLogs, flowLogs, flowLogsLoading } = useEditFlow()
const { isOwner } = useFlowOwner()

onMounted(async () => {
	await fetchFlowById(flowId)
	await fetchFlowLogs(flowId)
})

useHeaderTitle().setTitle('Flows')

// Add SEO meta tags
await useCustomHead({
	title: `${flowDetails.value?.name || 'Flow'} | Flow Details`,
	desc: flowDetails.value?.description || 'View flow details and automation steps',
	img: 'https://www.goalmatic.io/og2.png'
})

const currentTab = ref('editor')

// Function to refresh flow logs
const refreshFlowLogs = async () => {
  await fetchFlowLogs(flowId)
}

// Watch for tab changes to refresh logs when switching to the logs tab
watch(() => currentTab.value, (newTab) => {
  if (newTab === 'logs') {
    refreshFlowLogs()
  }
})



definePageMeta({
    layout: false
})
</script>

<style>

</style>
