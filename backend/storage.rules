rules_version = '2';

// Firebase Security Rules for Cloud Storage
// Based on: https://firebase.google.com/docs/storage/security
service firebase.storage {
  match /b/{bucket}/o {
    match /whatsapp-media/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write, delete: if false;
    }
    
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
