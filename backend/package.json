{"name": "taaskly", "private": true, "scripts": {"ds": "firebase deploy --only functions:", "deploy": "firebase deploy --only functions", "emu": "firebase emulators:start --import=./dummy_data --export-on-exit", "kill": "kill -9 <PID>", "k": "npx kill-port 9099 5001 8181 9000 9199 9299 4000 8085", "find_port": "sudo lsof -i :3000"}, "dependencies": {"convex": "^1.25.0"}, "devDependencies": {"@types/moment-timezone": "^0.5.30", "@types/node": "^22.13.1", "tsx": "^4.19.2", "typescript": "^5.7.3"}}