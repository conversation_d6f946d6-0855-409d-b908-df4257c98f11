{"name": "functions", "scripts": {"build:watch": "tsc --watch", "build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@ai-sdk/google": "^1.2.12", "@ai-sdk/openai": "^1.1.9", "@composio/core": "^0.1.36-next.13", "@sendgrid/mail": "^7.7.0", "@slack/webhook": "^6.1.0", "@sparticuz/chromium": "^138.0.1", "@upstash/qstash": "^2.7.23", "@upstash/redis": "^1.34.4", "@upstash/workflow": "^0.2.12", "ai": "^4.3.9", "api": "^5.0.7", "axios": "^1.3.3", "convex": "^1.12.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "firebase-admin": "^13.3.0", "firebase-functions": "^6.3.2", "genkit": "^0.9.12", "googleapis": "^140.0.1", "ioredis": "^5.4.2", "moment-timezone": "^0.5.45", "node-fetch": "^2.6.7", "openai": "^4.20.1", "playwright": "^1.54.1", "redis": "^4.7.0", "seamailer-nodejs": "^1.1.6", "uuid": "^10.0.0", "zod": "^3.24.1"}, "devDependencies": {"@types/uuid": "^10.0.0", "firebase-functions-test": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^4.9.0"}, "private": true}