import { WorkflowContext } from "@upstash/workflow";
import { FlowNode } from "../../../type";
import { processMentionsProps } from "../../../../../utils/processMentions";
import { generateAiFlowContext } from "../../../../../utils/generateAiFlowContext";
import { defaultGoalmaticAgent } from "../../../../../whatsapp/utils/WhatsappAgent";
import { initialiseAIChat } from "../../../../../ai/initialise";
import { v4 as uuidv4 } from 'uuid';

const askAi = async (context: WorkflowContext, step: FlowNode, previousStepResult: any) => {
    try {
        // Process all string fields in propsData first
        const processedProps = processMentionsProps(step.propsData, previousStepResult);
        
        // Generate AI content for AI-enabled fields and get updated props
        const { processedPropsWithAiContext } = await generateAiFlowContext(step, processedProps);
        
        const { 
            prompt
        } = processedPropsWithAiContext;

        if (!prompt) {
            return {
                success: false,
                error: 'Prompt is required'
            };
        }

        // Use the default Goalmatic agent for simple AI requests
        const agentData = defaultGoalmaticAgent;

        // Enhanced system prompt for different output formats
        const enhancedSystemInfo = `
        You are a helpful AI assistant. Respond to the user's prompt clearly and concisely.
        `;

        // Create the conversation
        const conversationHistory = [
            {
                role: 'user',
                content: prompt
            }
        ];

        // Use a unique session ID for this request
        const sessionId = uuidv4();

        // Enhanced agent with simple instructions
        const enhancedAgent = {
            ...agentData,
            spec: {
                ...agentData.spec,
                systemInfo: enhancedSystemInfo
            }
        };

        // Call the AI
        const aiResponse = await initialiseAIChat(conversationHistory, enhancedAgent, sessionId);

        return {
            success: true,
            payload: {
                ...processedPropsWithAiContext,
                aiResponse,
                sessionId
            }
        };

    } catch (error: any) {
        console.error('Error in Ask AI processing:', error);
        return {
            success: false,
            error: error?.message || 'Failed to process AI request'
        };
    }
};

export const askAiNode = {
    nodeId: 'ASK_AI',
    run: askAi
};
