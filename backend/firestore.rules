rules_version = '2';

service cloud.firestore {
  function isSignedIn() {
    return request.auth != null;
  }

  function isOwner(userId) {
    return request.auth.uid == userId;
  }

  // Validates that a timestamp exists and is not in the future.
  function validTimestamp(field) {
    return field is timestamp && field <= request.time;
  }

  match /databases/{database}/documents {

    // Users collection: holds user profiles.
    match /users/{userId} {
      allow create: if isSignedIn() &&
        isOwner(userId) &&
        request.resource.data.keys().hasAll(['email', 'created_at']) &&
        validTimestamp(request.resource.data.created_at);

      allow read: if isSignedIn() && isOwner(userId);

      allow update: if isSignedIn() &&
        isOwner(userId) &&
        request.resource.data.email == resource.data.email &&
        validTimestamp(request.resource.data.updated_at);

      allow delete: if false;

      // Chat Sessions subcollection: stores user's chat sessions with agents
      match /chatSessions/{sessionId} {
        allow create: if isSignedIn() &&
          isOwner(userId) &&
          request.resource.data.keys().hasAll(['id', 'created_at', 'messages']) &&
          validTimestamp(request.resource.data.created_at) &&
          request.resource.data.messages is list;

        allow read: if isSignedIn() && isOwner(userId);

        // Allow messages with role 'user', 'assistant', or 'tool'
        function validMessageRole(message) {
          return message.role == 'user' || message.role == 'assistant' || message.role == 'tool';
        }

        // Validate that all messages in the array have valid roles
        function validMessages(messages) {
          return messages.size() == 0 || messages.size() > 0 && messages[0].role != null && validMessageRole(messages[0]);
        }

        allow update: if isSignedIn() &&
          isOwner(userId) &&
          request.resource.data.keys().hasAll(['messages', 'updated_at']) &&
          validTimestamp(request.resource.data.updated_at) &&
          request.resource.data.messages is list &&
          validMessages(request.resource.data.messages);

        allow delete: if isSignedIn() && isOwner(userId);
      }

        // Integrations collection: settings for third-party integrations.
      match /integrations/{integrationId} {
        allow create: if isSignedIn() &&
          isOwner(userId) &&
          request.resource.data.keys().hasAll(['user_id', 'id', 'created_at']) &&
          validTimestamp(request.resource.data.created_at);

        allow read, update, delete: if isSignedIn() &&
          isOwner(userId) &&
          (request.method != "update" ||
            (request.resource.data.user_id == resource.data.user_id &&
             validTimestamp(request.resource.data.updated_at)));
    }
    }



    // Agents collection: AI agents that can be personal or public
    match /agents/{agentId} {
      allow create: if isSignedIn() &&
        isOwner(request.resource.data.creator_id) &&
        request.resource.data.keys().hasAll(['name', 'creator_id', 'created_at', 'description', 'public']) &&
        validTimestamp(request.resource.data.created_at);


      allow read: if  (resource.data.public == true || resource.data.creator_id == request.auth.uid);


      // Allow update and delete only to the owner
      allow update, delete: if isSignedIn() &&
        resource.data.creator_id == request.auth.uid &&
        (request.method != "update" ||
          (request.resource.data.creator_id == resource.data.creator_id &&
           validTimestamp(request.resource.data.updated_at)));
    }

    // Flows collection: user-defined automation flows that can be private or public
    match /flows/{flowId} {
      allow create: if isSignedIn() &&
        request.resource.data.creator_id == request.auth.uid &&
        request.resource.data.name is string &&
        request.resource.data.creator_id is string &&
        request.resource.data.created_at is timestamp &&
         request.resource.data.status is number &&
        request.resource.data.steps is list &&
        validTimestamp(request.resource.data.created_at);

      // Allow read access for public flows or if user is the creator
      allow read: if (resource.data.public == true || (isSignedIn() && resource.data.creator_id == request.auth.uid));

      allow update, delete: if isSignedIn() &&
        resource.data.creator_id == request.auth.uid &&
        (request.method != "update" ||
          (request.resource.data.creator_id == resource.data.creator_id &&
           validTimestamp(request.resource.data.updated_at)));

      // Logs subcollection: stores individual flow logs
      match /logs/{logId} {
        allow create: if isSignedIn() && (
          (!('creator_id' in request.resource.data) || request.resource.data.creator_id == request.auth.uid)
        );
        allow read: if isSignedIn() && (
          (!('creator_id' in resource.data) || resource.data.creator_id == request.auth.uid) ||
          (!('visibility' in resource.data) || resource.data.visibility == 'public') ||
          (resource.data.visibility == 'private' &&
            resource.data.allowed_users != null &&
            request.auth.uid in resource.data.allowed_users)
        );
        allow update, delete: if isSignedIn() && (
          (!('creator_id' in resource.data) || resource.data.creator_id == request.auth.uid)
        );
      }
    }

    // Tables collection: user-defined database tables with fields and records
    match /tables/{tableId} {
      allow create: if isSignedIn() &&
        request.resource.data.creator_id == request.auth.uid &&
        request.resource.data.name is string &&
        request.resource.data.creator_id is string &&
        request.resource.data.created_at is timestamp &&
        request.resource.data.fields is list &&
        validTimestamp(request.resource.data.created_at);

      allow read: if isSignedIn() && resource.data != null && (
        (!("creator_id" in resource.data) || resource.data.creator_id == request.auth.uid) ||
        (!("visibility" in resource.data) || resource.data.visibility == "public") ||
        (resource.data.visibility == "private" &&
         resource.data.allowed_users != null &&
         request.auth.uid in resource.data.allowed_users)
      );


      allow update, delete: if isSignedIn() &&
        (!("creator_id" in resource.data) || resource.data.creator_id == request.auth.uid) &&
        (request.method != "update" ||
          ((!("creator_id" in request.resource.data) && !("creator_id" in resource.data)) ||
           request.resource.data.creator_id == resource.data.creator_id) &&
          (!("updated_at" in request.resource.data) || validTimestamp(request.resource.data.updated_at)));

      // Records subcollection: individual records for each table
      match /records/{recordId} {
        allow create: if isSignedIn() &&
          get(/databases/$(database)/documents/tables/$(tableId)) != null &&
          get(/databases/$(database)/documents/tables/$(tableId)).data.creator_id == request.auth.uid &&
          request.resource.data.keys().hasAll(['id', 'created_at', 'updated_at']) &&
          validTimestamp(request.resource.data.created_at) &&
          validTimestamp(request.resource.data.updated_at);

        allow read: if isSignedIn() && (
          get(/databases/$(database)/documents/tables/$(tableId)) != null &&
          (
            get(/databases/$(database)/documents/tables/$(tableId)).data.creator_id == request.auth.uid  ||
            (
              get(/databases/$(database)/documents/tables/$(tableId)).data.visibility == "private" &&
              get(/databases/$(database)/documents/tables/$(tableId)).data.allowed_users != null &&
              request.auth.uid in get(/databases/$(database)/documents/tables/$(tableId)).data.allowed_users
            )
          )
        );

        allow update, delete: if isSignedIn() &&
          get(/databases/$(database)/documents/tables/$(tableId)) != null &&
          get(/databases/$(database)/documents/tables/$(tableId)).data.creator_id == request.auth.uid &&
          (request.method != "update" ||
            validTimestamp(request.resource.data.updated_at));
      }
    }

  

    // Processed messages collection: tracks WhatsApp message IDs to prevent duplicates
    // Only accessible by Cloud Functions (admin privileges)
    match /processed_messages/{messageId} {
      // Deny all user access - this collection is managed only by Cloud Functions
      allow read, write: if false;
    }

    // Public chats collection: shared chat sessions that are publicly accessible
    match /publicChats/{publicChatId} {
      // Allow anyone to read public chat sessions
      allow read: if true;
      
      // Only Cloud Functions can create/update/delete public chats
      allow create, update, delete: if false;
    }

    // Deny access to any other documents not explicitly covered above.
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
